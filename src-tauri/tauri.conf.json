{"$schema": "https://schema.tauri.app/config/2", "productName": "p-box", "version": "0.1.1", "identifier": "com.p-box.app", "build": {"frontendDist": "../src"}, "app": {"withGlobalTauri": true, "windows": [{"title": "p-box", "width": 1300, "height": 800}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "createUpdaterArtifacts": true}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDVENERCRkNBNEMyQTI1MApSV1JRb3NLay9OdlVCWXlxR0RRVUVWcmJRNTh2V3lRMXFOOWQrNXpSTTJ4SzltNnNLNGFGOXI0MAo=", "endpoints": ["{{TAURI_UPDATE_URL}}"], "dangerousInsecureTransportProtocol": true}}, "app_config": {"update_server": {"host": "https://newonet.com", "api_path": "/p-box/api/check-update/"}}}